import os

import click
import pandas as pd

from pytrust.databases import pg_write
from pytrust.supp_dues import cleaner, match, split, summary


@click.command()
@click.option("--file_name", help="original file name")
@click.option("--dir", help="output directory name")
def supp_dues(file_name, dir):
    """Accepts a csv and cleans according to db needs.
    Creates a Local Mismatch file to return to Finance.
    Warehouses clean contents in Postgres.
    'Authorized Dues By Local - All CSV_YYYY-MM.csv'
    """

    df = pd.read_csv(f"in_box/{file_name}")

    # stamp the file with the report month
    stamp = file_name[35:42]
    df["report_stamp"] = stamp

    # clean the file
    clean_df = cleaner.supp_cleaner(df)

    # write file to dummy table
    pg_write(clean_df)

    # match the file
    files = match.match(clean_df, "member_id", "ssn", dir)
    matched = files[2]
    unmatched = files[1]

    # split the files based on local
    splitter = split.Splitter()
    splitter.split_matched(matched, dir)
    splitter.split_unmatched(unmatched, dir)

    # create a summary file for finance
    summ = summary.Summarize()
    match_summ = summ.match_summary(matched)
    match_summ.to_excel(f"{dir}/Matched/Matched Summary.xlsx", index=False)

    unmatch_summ = summ.unmatch_summary(unmatched)
    unmatch_summ.to_excel(f"{dir}/UnMatched/UnMatched Summary.xlsx", index=False)

    # move the original to the output directory
    os.rename(f"in_box/{file_name}", f"{dir}/{file_name}")


if __name__ == "__main__":
    supp_dues()
