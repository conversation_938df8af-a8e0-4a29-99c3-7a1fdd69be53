import click

from pytrust import databases
from pytrust.council_match import cleaner, match, split


@click.command()
@click.option("--file_name", help="original file name")
@click.option("--file_column", help="file primary key column")
@click.option("--join_id", help="database primary key column")
@click.option("--title_arg", help="title for naming convention")
@click.option("--ssn", help="True/False if using ssns to match on")
@click.option("--dir", help="directory for output")
def council_match(file_name, file_column, join_id, title_arg, ssn, dir):
    """General file-matcher
    Requires that you identify the match column in both the DB and the file you provide
    Must supply True/False argument to indicate if using SSN's

    > council_match('Bad_Address_MonthYear.csv', 'member_id', 'mssn', 'Bad Address', True, 'Bad_Address_MonthYear-dir')
    """

    # Convert the ssn string to a boolean value
    ssn_bool = ssn.lower() == "true"

    clean_df = cleaner.cleaner(file_name, ssn_bool, file_column)

    databases.pg_write(clean_df)

    files = match.basic_match(clean_df, file_column, join_id, title_arg, dir)

    splitter = split.Splitter()
    splitter.split_unmatched(files[0], dir)
    splitter.split_matched(files[1], dir)


if __name__ == "__main__":
    council_match()
