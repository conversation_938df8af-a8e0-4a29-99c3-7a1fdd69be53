import os

import click

from pytrust import databases
from pytrust.auth_dues import cleaner, match, split


@click.command()
@click.option("--file", help="original file name")
@click.option("--dir", help="output directory name")
def auth_dues(file, dir):
    # accepts a file, and a directory name to place generated file

    clean_df = cleaner.cleaner(file)

    databases.pg_write(clean_df)

    files = match.match(clean_df, dir)

    split.split(files[0], dir)

    os.rename(f"in_box/{file}", f"{dir}/{file}")


if __name__ == "__main__":
    auth_dues()
