import click
import pandas as pd

from pytrust.databases import pg_connect


@click.command()
@click.option("--file_name", help="original file name")
def local_match(file_name):
    """accepts a file and checks if trust local == Dispatch Local
    Local Mis-Matches Only
    'Authorized Dues By Local - All CSV_YYYYMMDD.csv'
    'FUND - Member Contributions - CODUE.csv'
    """
    infile = pd.read_csv(f"in_box/{file_name}")

    infile.columns = (
        infile.columns.str.strip()
        .str.lower()
        .str.replace(" ", "_")
        .str.replace("(", "")
        .str.replace(")", "")
    )

    infile.rename(columns={"local": "source_local"}, inplace=True)

    infile.member_id = infile.member_id.apply(lambda x: "{0:0>9}".format(x))

    clean_df = infile.copy()

    query = "SELECT \
    ssn as mem_id,\
    ubcid, \
    local as member_local \
    FROM members \
    WHERE status IN ('GS','AR')"

    mems = pd.read_sql_query(sql=query, con=pg_connect())

    # left join mems to cleaned_df to find matches
    matches = pd.merge(
        clean_df, mems, left_on="member_id", right_on="mem_id", how="inner"
    )

    local_mismatch = matches.loc[~(matches["source_local"] == matches["member_local"])]

    local_mismatch.to_csv(f"LocalMismatch - {file_name}", index=False)


if __name__ == "__main__":
    local_match()
