import os
import zipfile
import zlib
from datetime import datetime

import pandas as pd

from pytrust.databases import pg_connect

df = pd.read_sql_table("csac_locator", pg_connect())

stamp = datetime.today().date()

df.to_csv(f"csac_locator_{stamp}.csv", index=False)

compress = zipfile.ZIP_DEFLATED

zipper = zipfile.ZipFile(f"csac_locator_{stamp}.zip", "w", compress)
zipper.write(f"csac_locator_{stamp}.csv")
zipper.close()

os.remove(f"csac_locator_{stamp}.csv")
# <NAME_EMAIL>
# See attached for member export for CSAC Locator policy
