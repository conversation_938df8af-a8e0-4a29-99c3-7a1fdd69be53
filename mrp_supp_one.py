import os

import click

from pytrust import databases
from pytrust.mrp_supp_one import cleaner, match, summary


@click.command()
@click.option("--file", help="original file name")
@click.option("--dir", help="output directory name")
def mrp_supp_one(file, dir):
    """accepts a file, and a directory name to place generated files"""
    clean_df = cleaner.cleaner(file)

    databases.pg_write(clean_df)

    files = match.match(clean_df, dir)

    # finance says they don't need the split files
    # split.split(files[0], dir)

    # summarize data
    summ = summary.Summarize()
    # matched summary
    summ.match_summary(files[2]).to_excel(
        f"{dir}/Matched/Match Summary.xlsx", index=False
    )

    # unmatched summary
    summ.unmatch_summary(files[1]).to_excel(
        f"{dir}/UnMatched/UnMatch Summary.xlsx", index=False
    )

    # os.rename(f"in_box/{file}", f"{dir}/{file}")


if __name__ == "__main__":
    mrp_supp_one()
