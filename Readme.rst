Activate poetry shell, or include 'poetry run' before python command 
    - poetry run python TrustyPy/<file_name>.py --arg_one='foo' --arg_two='bar'
    - poetry run python TrustyPy/vacation.py --file_name='vac_test_20210315.csv' --dir_name='Vac_Test_20210315' 


Click command-line argument helpers are documented
Ex: poetry run python TrustPy/vacation.py --help

Stand-Alone File Runners
    - vacation (WNDU and DUES)
        - ARGS: file_name, dir_name
    - auth_dues (Authorized Dues By Local - Percent Based_YYYYMMDD.csv)
        - ARGS: file_name, dir_name
    - mrp_supp_one (Supp.Dues Extra $1.00 Report - Month YYYY.csv)
        - ARGS: file_name, dir_name
    - ltd_pen (WNDUES and LTD) (SWRCC WNDUES LTD_Monthly.csv)
        - ARGS: file_name, dir_name
    - council_match (cupp runs, bad_address, internal etc)
        - ARGS: file_name, file_column, join_id, title_arg, ssn, dir
  
Local Mis-Matches Only
    - pytrust.local_mismatch
        - ARGS: file_name
        - ex: Authorized Dues By Local - All CSV_YYYYMMDD.csv
        - ex: FUND - Member Contributions - CODUE.csv

Health Code Update
    - pytrust.hc_update
        - ARGS: file_name
        - *DEPRECATED*

Drywall Admin Reports
    - pytrust.drywall_admin
        - ARGS: yyyymm


Authorized Dues with Window Dues Allocation
    - discard, do not run
