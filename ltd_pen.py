import os

import click

from pytrust import databases
from pytrust.ltd_pen import cleaner, match, split


@click.command()
@click.option("--file", help="original file name")
@click.option("--dir", help="output directory name")
def ltd_pen(file, dir):
    """accepts a file, and a directory name to place generated files"""

    # general cleaner
    clean_df = cleaner.cleaner(file, "member_id", True)

    databases.pg_write(clean_df)

    files = match.match(clean_df, "member_id", "ssn", dir)

    split.split_locals(files[0], dir)

    os.rename(f"in_box/{file}", f"{dir}/{file}")


if __name__ == "__main__":
    ltd_pen()
