import pandas as pd


def cleaner(filename, id_column="member_id", ssn_flag=True):

    infile = pd.read_csv(f"in_box/{filename}")
    infile.columns = (
        infile.columns.str.strip()
        .str.lower()
        .str.replace(" ", "_")
        .str.replace("(", "")
        .str.replace(")", "")
    )

    infile["local_%"] = infile["local_%"].str.replace("$", "").astype("float")
    infile = infile[infile["local_%"] != 0]

    infile.rename(columns={"local": "source_local"}, inplace=True)

    if ssn_flag is True:
        infile[f"{id_column}"] = infile[f"{id_column}"].apply(
            lambda x: "{0:0>9}".format(x)
        )

    return infile
