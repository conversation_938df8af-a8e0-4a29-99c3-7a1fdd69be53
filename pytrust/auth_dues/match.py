import os
import pandas as pd
from pandas.io import sql
from pytrust.databases import pg_connect


def match(org_df, proj_dir, match_column="member_id", join_id="ssn"):
    """inner join on designated column
    match column is DataFrame column, assumes member_id
    join_id is members table column name to match on
    returns other id column {select_field}, status, dues, member local
    """

    os.makedirs(f"{proj_dir}/Matched/Split")

    if join_id == "ubcid":
        select_field = "ssn"
    elif join_id == "ssn":
        select_field = "ubcid"

    tablename = "zzz_pytable"

    query = f"SELECT zzz.*,\
    {select_field}, \
    local AS member_local \
    FROM {tablename} zzz \
    JOIN members mem \
    ON mem.{join_id}=zzz.{match_column}"

    matches = pd.read_sql_query(sql=query, con=pg_connect())

    un_matched = org_df[
        ~org_df[[f"{match_column}"]]
        .apply(tuple, 1)
        .isin(matches[[f"{match_column}"]].apply(tuple, 1))
    ]

    master_file = pd.concat([matches, un_matched])

    working_master = master_file.copy()

    master_file.loc["Total"] = (
        master_file[["local_%"]].sum().reindex(master_file.columns, fill_value="")
    )

    local_mismatch = matches.loc[~(matches["source_local"] == matches["member_local"])]

    master_file.to_excel(f"{proj_dir}/Master File.xlsx", index=False)
    un_matched.to_excel(f"{proj_dir}/Un Matched.xlsx", index=False)
    matches.to_excel(f"{proj_dir}/Matched/Matched.xlsx", index=False)
    local_mismatch.to_excel(f"{proj_dir}/Matched/Local MisMatch.xlsx", index=False)

    return working_master, un_matched, matches, local_mismatch
