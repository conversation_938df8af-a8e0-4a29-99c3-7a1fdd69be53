def split(master_df, proj_dir):

    local_group = master_df.groupby("member_local")

    frames = [group for _, group in local_group]

    for i in frames:
        locs = list(set(i.member_local))
        i.loc["Total"] = i[["local_%"]].sum().reindex(i.columns, fill_value="")
        i.to_excel(
            f"{proj_dir}/Matched/Split/{proj_dir} - Local {int(locs[0])}.xlsx",
            index=False,
        )

    print(f"{len(frames)} files written")

    return frames
