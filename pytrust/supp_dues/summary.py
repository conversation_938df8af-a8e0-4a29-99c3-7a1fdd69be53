import pandas as pd


class Summarize:
    """Functions to summarize the data for Finance.

    match_summary - summarizes the match file
        -- uses member_local

    unmatch_summary - summarizes the unmatch file
        -- uses trust_local
    """

    def match_summary(self, df):
        # Ensure changes to 'df' are done safely
        df.loc[:, "hours"] = pd.to_numeric(df["hours"], errors="coerce")
        df.loc[:, "recip_amt"] = pd.to_numeric(df["recip_amt"], errors="coerce")

        # Group by 'member_local' and sum 'hours' and 'recip_amt'
        summary = (
            df.groupby("member_local")
            .agg({"hours": "sum", "recip_amt": "sum"})
            .reset_index()
        )

        return summary

    def unmatch_summary(self, df):
        # Ensure changes to 'df' are done safely
        df.loc[:, "hours"] = pd.to_numeric(df["hours"], errors="coerce")
        df.loc[:, "recip_amt"] = pd.to_numeric(df["recip_amt"], errors="coerce")

        # Group by 'trust_local' and sum 'hours' and 'recip_amt'
        summary = (
            df.groupby("trust_local")
            .agg({"hours": "sum", "recip_amt": "sum"})
            .reset_index()
        )

        return summary
