import numpy as np
import pandas as pd


def supp_cleaner(df):
    df.columns = (
        df.columns.str.strip()
        .str.lower()
        .str.replace(" ", "_")
        .str.replace("(", "")
        .str.replace(")", "")
        .str.replace("%", "share")
    )

    df.rename(columns={"local": "trust_local"}, inplace=True)

    # clean columns individually, yes its that bad
    # dollar signs, parentheses, blanks, empty strings
    df.grswge = (
        df.grswge.replace("[\\$,)]", "", regex=True)
        .replace("[(]", "-", regex=True)
        .replace(" ", "", regex=True)
        .replace("", np.NaN, regex=True)
    )

    # dollar signs, parentheses, blanks, empty strings
    df.recip_amt = (
        df.recip_amt.replace("[\\$,)]", "", regex=True)
        .replace("[(]", "-", regex=True)
        .replace(" ", "", regex=True)
        .replace("", np.NaN, regex=True)
    )

    # dollar signs, blanks, empty strings
    df.local_share = df.local_share.replace("\\$", "", regex=True).replace(
        " ", "", regex=True
    ).replace("", np.NaN, regex=True).replace("[()]", "", regex=True).astype(
        float
    ) * df[
        "recip_amt"
    ].str.contains(
        "(", regex=False
    ).map(
        {True: -1, False: 1}
    )

    # dollar signs, blanks, empty strings
    df.council_share = (
        df.local_share.replace("\\$", "", regex=True)
        .replace(" ", "", regex=True)
        .replace("", np.NaN, regex=True)
    )

    # commas, blanks, empty strings
    df.hours = (
        df.hours.replace(",", "", regex=True)
        .replace(" ", "", regex=True)
        .replace("", np.NaN, regex=True)
    )
    df.hours = pd.to_numeric(df["hours"], errors="coerce")

    # remove erroneous whitespace
    df.area = df.area.replace(" ", "", regex=True)
    df.employer = df.employer.replace(" ", "", regex=True)
    df.last_name = df.last_name.replace(" ", "", regex=True)
    df.first_name = df.first_name.replace(" ", "", regex=True)
    df.wkmo = df.wkmo.replace(" ", "", regex=True)

    # pad zeroes to the left of ssn
    df.member_id = df.member_id.apply(lambda x: "{0:0>9}".format(x))

    return df
