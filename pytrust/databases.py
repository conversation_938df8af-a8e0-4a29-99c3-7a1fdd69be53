import json

import sqlalchemy as sql


def pg_connect():
    with open("misc/creds/pg_creds.json", "r") as credfile:
        pg_data = credfile.read()
    pgcreds = json.loads(pg_data)
    engine = sql.create_engine(
        f"postgresql://{pgcreds['uid']}:{pgcreds['pwd']}@{pgcreds['host']}:{pgcreds['port']}/{pgcreds['db']}"
    )

    if engine:
        print("connected")
        pass
    else:
        print("not connected")
        exit
    return engine


def pg_write(clean_df, tablename="zzz_pytable", exists="replace"):
    # tablename = "zzz_pytable"
    clean_df.to_sql(
        name=tablename,
        con=pg_connect(),
        if_exists=exists,
        index=False,
        chunksize=10000,
    )
    print("table written")


def pg_command(command):
    engine = pg_connect()
    with engine.connect() as connection:
        result = connection.execute(sql.text(command))
    return result
