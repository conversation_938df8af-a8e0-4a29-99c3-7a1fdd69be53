import pandas as pd


def cleaner(filename, id_column="member_id", ssn_flag=True):

    infile = pd.read_csv(f"in_box/{filename}")

    infile.columns = (
        infile.columns.str.strip()
        .str.lower()
        .str.replace(" ", "_")
        .str.replace("(", "")
        .str.replace(")", "")
        .str.replace("$", "")
    )

    infile["recip_amt"] = infile["recip_amt"].replace("[\$,]", "", regex=True)
    infile["recip_amt"] = infile["recip_amt"].replace("[()]", "", regex=True).astype(
        float
    ) * infile["recip_amt"].str.contains("(", regex=False).map({True: -1, False: 1})
    infile["hours"] = pd.to_numeric(infile["hours"], errors="coerce")

    # do the same things for ind__amt
    infile["ind__amt"] = infile["ind__amt"].replace("[\$,]", "", regex=True)
    infile["ind__amt"] = infile["ind__amt"].replace("[()]", "", regex=True).astype(
        float
    ) * infile["ind__amt"].str.contains("(", regex=False).map({True: -1, False: 1})

    infile.rename(columns={"local": "trust_local"}, inplace=True)

    if ssn_flag is True:
        infile[f"{id_column}"] = infile[f"{id_column}"].apply(
            lambda x: "{0:0>9}".format(x)
        )

    return infile
