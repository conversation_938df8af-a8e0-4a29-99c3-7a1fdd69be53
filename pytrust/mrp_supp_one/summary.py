import pandas as pd


class Summarize:
    """Functions to summarize the data for Finance.

    match_summary - summarizes the match file
        -- uses member_local

    unmatch_summary - summarizes the unmatch file
        -- uses trust_local
    """

    def match_summary(self, df):
        # Group by 'member_local' and sum 'hours' and 'ind__amt'
        summary = (
            df.groupby("member_local")
            .agg({"hours": "sum", "ind__amt": "sum"})
            .reset_index()
        )

        return summary

    def unmatch_summary(self, df):
        # Group by 'trust_local' and sum 'hours' and 'ind__amt'
        summary = (
            df.groupby("trust_local")
            .agg({"hours": "sum", "ind__amt": "sum"})
            .reset_index()
        )

        return summary
