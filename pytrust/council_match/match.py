import os

import pandas as pd
from pytrust import databases


def basic_match(org_df, match_column, join_id, title_arg, dir):
    """inner join on designated column
    match column is DataFrame column, assumes member_id
    join_id is members table column name to match on
    gets {select_field}, status, dues, member local from database
    writes matched and unmatched to csv
    returns a matched, unmatched, and master file
    """

    os.makedirs(f"{dir}/Matched/Split")
    os.makedirs(f"{dir}/UnMatched/Split")

    if join_id == "ubcid":
        select_field = "ssn"
    elif join_id == "ssn":
        select_field = "ubcid"

    tablename = "zzz_pytable"

    query = f"SELECT zzz.*, mem.{select_field}, mem.status, mem.class, mem.craft, mem.dues, mem.paid_thru, mem.local AS member_local FROM {tablename} zzz JOIN zzmember_base mem ON mem.{join_id}=zzz.{match_column}::VARCHAR"

    matches = pd.read_sql_query(sql=query, con=databases.pg_connect())

    un_matched = org_df[
        ~org_df[[f"{match_column}"]]
        .apply(tuple, 1)
        .isin(matches[[f"{match_column}"]].apply(tuple, 1))
    ]

    un_matched.to_csv(f"{dir}/UnMatched/{title_arg}_Un Matched.csv", index=False)
    matches.to_csv(f"{dir}/Matched/{title_arg}_Matched.csv", index=False)

    return un_matched, matches
