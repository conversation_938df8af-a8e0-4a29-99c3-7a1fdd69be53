import pandas as pd


def cleaner(filename, ssn_flag, id_column):
    # Specify the dtype for the id_column to ensure it's read as a string
    infile = pd.read_csv(f"in_box/{filename}", dtype={id_column: str})

    infile.columns = (
        infile.columns.str.strip()
        .str.lower()
        .str.replace(" ", "_")
        .str.replace("(", "")
        .str.replace(")", "")
    )

    infile.rename(columns={"local": "source_local"}, inplace=True)

    # strip whitespace from id column for a better join, only for UBCIDs!
    if not ssn_flag:
        infile[id_column] = infile[id_column].str.strip()

    if ssn_flag:
        # Convert float to integer, then to string, and finally pad with leading zeroes
        infile[id_column] = infile[id_column].apply(
            lambda x: "{:09}".format(int(x)) if not pd.isna(x) else x
        )

    return infile
