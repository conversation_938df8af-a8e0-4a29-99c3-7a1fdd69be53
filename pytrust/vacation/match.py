import os

import pandas as pd
from pytrust.databases import pg_connect


def match(org_df, match_column, join_id, proj_dir):
    """inner join on designated column
    match column is DataFrame column, assumes member_id
    join_id is members table column name to match on
    returns other id column {select_field}, status, dues, member local
    """

    os.makedirs(f"{proj_dir}/Matched/Split")
    os.makedirs(f"{proj_dir}/UnMatched/Split")

    if join_id == "ubcid":
        select_field = "ssn"
    elif join_id == "ssn":
        select_field = "ubcid"

    query = f"SELECT a.*, \
        {select_field}, \
        b.local AS member_local, \
        b.status AS union_status \
    FROM zzz_pytable a \
    JOIN members b \
        ON b.{join_id}::varchar=a.{match_column}::varchar"

    matches = pd.read_sql_query(sql=query, con=pg_connect())

    # alternate un-matched method
    # common = org_df.merge(matches, on=f"{match_column}")
    # un_matched = org_df[~org_df.member_id.isin(common.member_id)]

    un_matched = org_df[
        ~org_df[[f"{match_column}"]]
        .apply(tuple, 1)
        .isin(matches[[f"{match_column}"]].apply(tuple, 1))
    ]

    master_file = pd.concat([matches, un_matched])

    # wh_amount to float, remove commas
    master_file.wh_amount = master_file.wh_amount.replace(",", "", regex=True).astype(
        float
    )

    working_master = master_file.copy()

    master_file.loc["Total"] = (
        master_file[["wh_amount"]].sum().reindex(master_file.columns, fill_value="")
    )

    local_mismatch = matches.loc[~(matches["source_local"] == matches["member_local"])]

    master_file.to_excel(f"{proj_dir}/Master File.xlsx", index=False)

    matches.to_excel(f"{proj_dir}/Matched/Matched.xlsx", index=False)
    un_matched.to_excel(f"{proj_dir}/UnMatched/UnMatched.xlsx", index=False)
    local_mismatch.to_excel(f"{proj_dir}/Matched/Local MisMatch.xlsx", index=False)

    return working_master, un_matched, matches, local_mismatch
