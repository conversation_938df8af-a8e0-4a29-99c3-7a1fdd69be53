import pandas as pd


# basic cleaning for files rec'd from CSAC
def cleaner(filename, id_column, ssn_flag):
    infile = pd.read_csv(f"in_box/{filename}")
    # clean column names
    infile.columns = (
        infile.columns.str.strip()
        .str.lower()
        .str.replace(" ", "_")
        .str.replace("(", "")
        .str.replace(")", "")
    )
    # convert wh_amount to float for math
    # infile.wh_amount = infile.wh_amount.str.replace(",", "")
    infile.wh_amount = infile.wh_amount.astype(float)
    # remove rows where wh_amount is null or na
    infile = infile[infile.wh_amount.notna()]
    infile = infile[infile.wh_amount != 0]

    infile.rename(columns={"local": "source_local"}, inplace=True)

    if ssn_flag is True:
        infile[f"{id_column}"] = infile[f"{id_column}"].apply(
            lambda x: "{0:0>9}".format(x)
        )

    return infile
