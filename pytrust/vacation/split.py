class Splitter:
    def split_matched(self, master_df, proj_dir):
        local_group = master_df.groupby("member_local")

        frames = [group for _, group in local_group]

        for i in frames:
            locs = list(set(i.member_local))
            # create a subtotal for wh_amount in each file
            i.loc["Total"] = i[["wh_amount"]].sum().reindex(i.columns, fill_value="")
            i.to_excel(
                f"{proj_dir}/Matched/Split/{proj_dir} - Local {int(locs[0])}.xlsx",
                index=False,
            )

        print(f"{len(frames)} matched split files")

        return frames

    def split_unmatched(self, master_df, proj_dir):
        local_group = master_df.groupby("source_local")

        frames = [group for _, group in local_group]

        for i in frames:
            locs = list(set(i.source_local))
            i.loc["Total"] = i[["wh_amount"]].sum().reindex(i.columns, fill_value="")
            i.to_excel(
                f"{proj_dir}/UnMatched/Split/{proj_dir} - UnMatched - Local {int(locs[0])}.xlsx"
            )

        print(f"{len(frames)} un-matched split files")
