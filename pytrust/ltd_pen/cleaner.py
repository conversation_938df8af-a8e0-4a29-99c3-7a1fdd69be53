import pandas as pd


def cleaner(filename, id_column="member_id", ssn_flag=True):
    infile = pd.read_csv(f"in_box/{filename}", dtype={"Member ID": str})
    infile.columns = (
        infile.columns.str.strip()
        .str.lower()
        .str.replace(" ", "_")
        .str.replace("(", "")
        .str.replace(")", "")
    )
    # change wh_amount column to float
    infile.iloc[:, 5] = (
        infile[infile.columns[5:]].replace("[\$,]", "", regex=True).astype(float)
    )
    # remove wh_amount of zero
    infile = infile[infile.iloc[:, 5] > 0]

    infile.rename(columns={"local": "source_local"}, inplace=True)

    if ssn_flag is True:
        infile[f"{id_column}"] = infile[f"{id_column}"].apply(
            lambda x: "{0:0>9}".format(x)
        )

    return infile
