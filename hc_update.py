import pandas as pd
import sqlalchemy as sql
from pytrust import databases

import click


# command line arguments
@click.command()
@click.option("--file_name", help="health code file name")
def update_codes(file_name):
    """accepts an xlsx file from <PERSON><PERSON>, updates x_hc_codes

    > update_codes('Member Status Report OP - MM.DD.YYYY thru MM.DD.YYYY.xlsx')
    """

    engine = databases.carp_connect()

    infile = pd.ExcelFile(f"in_box/{file_name}")

    # class ExcelFile is not iterable, open them separately
    two_add = pd.read_excel(infile, 0)
    one_add = pd.read_excel(infile, 1)

    # combine them to iterate cleaning
    dataframes = [two_add, one_add]
    for df in dataframes:
        df.columns = (
            df.columns.str.strip()
            .str.lower()
            .str.replace(" ", "_")
            .str.replace("(", "")
            .str.replace(")", "")
        )
        df["member_id"] = df["member_id"].apply(lambda x: "{0:0>9}".format(x))

    cleaned = dataframes[0].append(pd.DataFrame(data=dataframes[1]), ignore_index=True)

    update_file = cleaned[["member_id", "dispatch_code", "sts_action_date"]]

    update_file.rename(
        columns={
            "member_id": "memID",
            "dispatch_code": "HealthCareCode",
            "sts_action_date": "StartDate",
        },
        inplace=True,
        copy=False,
        # copy = False suppresses warning, copy value is not evaluated where inplace=True
    )

    update_file.to_sql(
        "xHC_Codes",
        con=engine,
        index=False,
        dtype={
            "member_id": sql.types.VARCHAR(length=9),
            "dispatch_code": sql.types.Numeric(1),
            "sts_action_date": sql.types.DateTime(),
        },
        if_exists="append",
    )


if __name__ == "__main__":
    update_codes()
