import os
import pandas as pd
import numpy as np
from pytrust import databases
import click


@click.command()
@click.option("--yyyymm", help="Year Month Stamp for Dispatches")
def monthly_dispatches(yyyymm):
    """Generates a file for each contract area.
    Dispatches by agreement, specified by <PERSON> and <PERSON>

    > monthly_dispatches(YYYYMM)
    """

    if len(yyyymm) > 0:

        os.makedirs(f"DWAdmin Reports {yyyymm}")

        engine = databases.carp_connect()

        df = pd.read_sql("select * from py_drywall_admin", engine)

        # states = list(filter(None, df.State.unique()))
        states = ["AZ", "CA", "NN", "SN", "UT"]

        for i in states:
            p = df.pivot_table(
                values=["Dispatches"],
                index=[
                    "Agreement",
                    "Contractor",
                    "License",
                    "State",
                    "Project",
                    "Address",
                    "Month",
                ],
                aggfunc=[np.sum],
                fill_value=0,
                margins=True,
            )

            state_report = p.query(f"State == ['{i}']")
            month_report = state_report.query(f"Month == ['{yyyymm}']")

            month_report.to_excel(f"DWAdmin Reports {yyyymm}/{i} Dispatches.xlsx")


if __name__ == "__main__":
    monthly_dispatches()
