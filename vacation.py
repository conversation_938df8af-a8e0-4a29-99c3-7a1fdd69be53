import os

import click

from pytrust.databases import pg_write
from pytrust.vacation import cleaner, match, split


@click.command()
@click.option("--file", help="original file name")
@click.option("--dir", help="output directory name")
def vacation(file, dir):
    # accepts a file, and a directory name to place generated files

    # clean the file
    clean_df = cleaner.cleaner(file, "member_id", True)

    # convert wh_amount to float

    # write the file
    pg_write(clean_df)

    # match (query the file)
    files = match.match(clean_df, "member_id", "ssn", dir)

    # split to Local files
    splitter = split.Splitter()
    splitter.split_matched(files[0], dir)
    splitter.split_unmatched(files[1], dir)
    # move the original to the output directory
    os.rename(f"in_box/{file}", f"{dir}/{file}")


if __name__ == "__main__":
    vacation()
